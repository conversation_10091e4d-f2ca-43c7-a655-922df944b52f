/* Sign In Page Specific Styles */

.signin-body {
  overflow-x: hidden;
  min-height: 100vh;
}

/* Enhanced Background */
.signin-blur {
  background: radial-gradient(
    ellipse at center,
    rgba(124, 58, 237, 0.4) 0%,
    rgba(236, 72, 153, 0.3) 50%,
    rgba(245, 158, 11, 0.2) 100%
  );
  animation: pulseGlow 8s ease-in-out infinite;
}

@keyframes pulseGlow {
  0%, 100% { 
    transform: rotate(-25deg) translateZ(-30px) scale(1);
    opacity: 0.6;
  }
  50% { 
    transform: rotate(-25deg) translateZ(-30px) scale(1.2);
    opacity: 0.8;
  }
}

/* Floating Particles */
.signin-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -5;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: linear-gradient(45deg, #7c3aed, #ec4899);
  border-radius: 50%;
  animation: floatParticle 15s linear infinite;
}

.particle:nth-child(1) {
  left: 10%;
  animation-duration: 12s;
}

.particle:nth-child(2) {
  left: 30%;
  animation-duration: 18s;
}

.particle:nth-child(3) {
  left: 50%;
  animation-duration: 15s;
}

.particle:nth-child(4) {
  left: 70%;
  animation-duration: 20s;
}

.particle:nth-child(5) {
  left: 90%;
  animation-duration: 14s;
}

@keyframes floatParticle {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

/* Container */
.signin-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header Adjustments */
.signin-header {
  background: rgba(15, 15, 35, 0.95);
  backdrop-filter: blur(30px);
}

.btn-signup {
  background: transparent;
  color: #7c3aed;
  border: 2px solid #7c3aed;
  padding: 0.8rem 2rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-signup:hover {
  background: #7c3aed;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(124, 58, 237, 0.4);
}

/* Main Content */
.signin-main {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 2rem 0;
}

.signin-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  min-height: 80vh;
  gap: 4rem;
  padding: 0 2rem;
}

/* Welcome Section */
.signin-welcome {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transform: translateZ(30px);
}

.welcome-content {
  max-width: 500px;
  text-align: left;
}

.welcome-title {
  font-size: 4rem;
  font-weight: 900;
  line-height: 1.1;
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #ffffff 0%, #7c3aed 30%, #ec4899 70%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: gradientShift 4s ease-in-out infinite;
}

.title-line {
  display: block;
  transform: translateX(-20px);
  opacity: 0;
  animation: slideInLeft 1s ease forwards;
}

.title-line:nth-child(2) {
  animation-delay: 0.3s;
}

@keyframes slideInLeft {
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.welcome-description {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.7;
  margin-bottom: 3rem;
  opacity: 0;
  animation: fadeInUp 1s ease 0.6s forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.welcome-features {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateX(-30px);
  animation: slideInFeature 0.8s ease forwards;
}

.feature-item:nth-child(1) { animation-delay: 0.9s; }
.feature-item:nth-child(2) { animation-delay: 1.2s; }
.feature-item:nth-child(3) { animation-delay: 1.5s; }

@keyframes slideInFeature {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(10px);
  box-shadow: 0 10px 30px rgba(124, 58, 237, 0.2);
}

.feature-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #7c3aed, #ec4899);
  border-radius: 10px;
}

.feature-item span {
  color: #ffffff;
  font-weight: 600;
}

/* Sign In Form Container */
.signin-form-container {
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateZ(40px);
}

.signin-form-wrapper {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(30px);
  border-radius: 25px;
  padding: 3rem;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 450px;
  opacity: 0;
  transform: translateY(30px);
  animation: slideInForm 1s ease 0.3s forwards;
}

@keyframes slideInForm {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.form-header h2 {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #7c3aed 50%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.form-header p {
  color: rgba(255, 255, 255, 0.6);
  font-size: 1rem;
}

/* Form Styles */
.signin-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
  font-size: 0.9rem;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-wrapper input {
  width: 100%;
  padding: 1.2rem 3rem 1.2rem 3rem;
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.input-wrapper input:focus {
  outline: none;
  border-color: #7c3aed;
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.2);
  transform: translateY(-2px);
}

.input-wrapper input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.input-icon {
  position: absolute;
  left: 1rem;
  font-size: 1.2rem;
  pointer-events: none;
}

.toggle-password {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.toggle-password:hover {
  color: #7c3aed;
  transform: scale(1.1);
}

/* Form Options */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0.5rem 0;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.checkbox-wrapper input {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-wrapper input:checked + .checkmark {
  background: linear-gradient(135deg, #7c3aed, #ec4899);
  border-color: #7c3aed;
}

.checkbox-wrapper input:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.forgot-password {
  color: #7c3aed;
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.forgot-password:hover {
  color: #ec4899;
  text-shadow: 0 0 10px rgba(236, 72, 153, 0.5);
}

/* Sign In Button */
.signin-btn {
  width: 100%;
  background: linear-gradient(135deg, #7c3aed 0%, #ec4899 100%);
  color: white;
  border: none;
  padding: 1.3rem 2rem;
  border-radius: 15px;
  cursor: pointer;
  font-weight: 700;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  margin: 1rem 0;
}

.signin-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(124, 58, 237, 0.5);
  background: linear-gradient(135deg, #8b5cf6 0%, #f472b6 100%);
}

.signin-btn:active {
  transform: translateY(-1px);
}

.signin-btn .btn-glow {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.signin-btn:hover .btn-glow {
  opacity: 1;
}

/* Divider */
.divider {
  text-align: center;
  margin: 1.5rem 0;
  position: relative;
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.9rem;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.divider span {
  background: rgba(15, 15, 35, 0.9);
  padding: 0 1rem;
  position: relative;
  z-index: 1;
}

/* Social Sign In */
.social-signin {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 0.9rem;
}

.social-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.social-icon {
  font-size: 1.2rem;
}

/* Sign Up Link */
.signup-link {
  text-align: center;
  margin-top: 1rem;
}

.signup-link p {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
}

.signup-link a {
  color: #7c3aed;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.signup-link a:hover {
  color: #ec4899;
  text-shadow: 0 0 10px rgba(236, 72, 153, 0.5);
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 10000;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  border-radius: 25px;
  padding: 3rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  max-width: 400px;
  width: 90%;
}

.modal-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.modal-content h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #ffffff;
}

.modal-content p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.modal-btn {
  background: linear-gradient(135deg, #7c3aed 0%, #ec4899 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 15px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.modal-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(124, 58, 237, 0.5);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Loading Overlay */
.loading-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  z-index: 9999;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 2rem;
}

.loading-spinner {
  position: relative;
  width: 80px;
  height: 80px;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top: 3px solid #7c3aed;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-ring:nth-child(2) {
  width: 60px;
  height: 60px;
  top: 10px;
  left: 10px;
  border-top-color: #ec4899;
  animation-duration: 1.5s;
  animation-direction: reverse;
}

.spinner-ring:nth-child(3) {
  width: 40px;
  height: 40px;
  top: 20px;
  left: 20px;
  border-top-color: #f59e0b;
  animation-duration: 2s;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-overlay p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.2rem;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .signin-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .welcome-title {
    font-size: 3rem;
  }
  
  .signin-form-wrapper {
    max-width: 500px;
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .signin-content {
    padding: 0 1rem;
  }
  
  .welcome-title {
    font-size: 2.5rem;
  }
  
  .signin-form-wrapper {
    padding: 2rem;
  }
  
  .social-signin {
    grid-template-columns: 1fr;
  }
  
  .form-options {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .welcome-features {
    gap: 1rem;
  }
  
  .feature-item {
    padding: 0.8rem;
  }
  
  .signin-form-wrapper {
    padding: 1.5rem;
  }
  
  .input-wrapper input {
    padding: 1rem 2.5rem;
  }
  
  .modal-content {
    padding: 2rem;
  }
}
