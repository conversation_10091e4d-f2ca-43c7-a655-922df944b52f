<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>3D Web Experience - Next Generation UI/UX</title>
    <meta
      name="description"
      content="Experience the future of web design with stunning 3D interfaces and immersive user experiences"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="style.css" />
  </head>
  <body>
    <!-- Background Elements -->
    <div class="background-elements">
      <div class="layer-blur"></div>
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
      </div>
    </div>

    <!-- Main Container -->
    <div class="container">
      <!-- Header Section -->
      <header class="header">
        <div class="header-content">
          <div class="logo-section">
            <h1 class="logo">
              <span class="logo-text">3D</span>
              <span class="logo-accent">Web</span>
            </h1>
          </div>

          <nav class="navigation" role="navigation">
            <ul class="nav-list">
              <li class="nav-item">
                <a href="#about" class="nav-link">About</a>
              </li>
              <li class="nav-item">
                <a href="#features" class="nav-link">Features</a>
              </li>
              <li class="nav-item">
                <a href="#portfolio" class="nav-link">Portfolio</a>
              </li>
              <li class="nav-item">
                <a href="#contact" class="nav-link">Contact</a>
              </li>
            </ul>
          </nav>

          <div class="header-actions">
            <button class="btn-signing" aria-label="Sign In">
              <span>Sign In</span>
              <div class="btn-glow"></div>
            </button>
          </div>
        </div>
      </header>

      <!-- Main Content -->
      <main class="main-content">
        <section class="hero-section">
          <!-- Floating Tag -->
          <div class="tag-box" role="banner">
            <div class="tag">
              <span>✨ Introducing</span>
            </div>
          </div>

          <!-- Hero Content -->
          <div class="content">
            <div class="hero-text">
              <h1 class="hero-title">
                <span class="title-line">Next-Gen</span>
                <span class="title-line">3D Web</span>
                <span class="title-line">Experience</span>
              </h1>

              <p class="hero-description">
                Immerse yourself in the future of web design with stunning 3D
                interfaces, interactive elements, and cutting-edge visual
                effects that bring your digital experience to life.
              </p>
            </div>

            <!-- Action Buttons -->
            <div class="hero-actions">
              <a
                href="#explore"
                class="btn-primary btn-get-started"
                role="button"
              >
                <span>Explore Now</span>
                <div class="btn-shine"></div>
              </a>
              <a
                href="#learn"
                class="btn-secondary btn-signing-main"
                role="button"
              >
                <span>Learn More</span>
                <div class="btn-border"></div>
              </a>
            </div>

            <!-- Stats Section -->
            <div class="stats-section">
              <div class="stat-item">
                <span class="stat-number">100+</span>
                <span class="stat-label">3D Components</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">50k+</span>
                <span class="stat-label">Happy Users</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">99%</span>
                <span class="stat-label">Performance</span>
              </div>
            </div>
          </div>
        </section>
      </main>

      <!-- Scroll Indicator -->
      <div class="scroll-indicator">
        <div class="scroll-line"></div>
        <span class="scroll-text">Scroll to explore</span>
      </div>
    </div>

    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
      <div class="loader">
        <div class="loader-ring"></div>
        <div class="loader-text">Loading 3D Experience...</div>
      </div>
    </div>

    <!-- JavaScript -->
    <script>
      // Simple loading screen
      window.addEventListener("load", function () {
        const loadingScreen = document.getElementById("loadingScreen");
        setTimeout(() => {
          loadingScreen.style.opacity = "0";
          setTimeout(() => {
            loadingScreen.style.display = "none";
          }, 500);
        }, 1500);
      });

      // Smooth scrolling for navigation links
      document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute("href"));
          if (target) {
            target.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          }
        });
      });
    </script>
  </body>
</html>
