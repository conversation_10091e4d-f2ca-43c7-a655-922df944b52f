<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Portfolio - 3D Web Experience</title>
    <meta name="description" content="Explore our stunning 3D web design portfolio and creative projects" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet" />
    <link rel="stylesheet" href="style.css" />
    <link rel="stylesheet" href="pages.css" />
  </head>
  <body>
    <!-- Background Elements -->
    <div class="background-elements">
      <div class="layer-blur"></div>
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
      </div>
    </div>

    <!-- Main Container -->
    <div class="container">
      <!-- Header Section -->
      <header class="header">
        <div class="header-content">
          <div class="logo-section">
            <h1 class="logo">
              <a href="index.html">
                <span class="logo-text">3D</span>
                <span class="logo-accent">Web</span>
              </a>
            </h1>
          </div>
          
          <nav class="navigation" role="navigation">
            <ul class="nav-list">
              <li class="nav-item"><a href="about.html" class="nav-link">About</a></li>
              <li class="nav-item"><a href="features.html" class="nav-link">Features</a></li>
              <li class="nav-item"><a href="portfolio.html" class="nav-link active">Portfolio</a></li>
              <li class="nav-item"><a href="contact.html" class="nav-link">Contact</a></li>
            </ul>
          </nav>
          
          <div class="header-actions">
            <button class="btn-signing" aria-label="Sign In">
              <span>Sign In</span>
              <div class="btn-glow"></div>
            </button>
          </div>
        </div>
      </header>

      <!-- Main Content -->
      <main class="main-content page-content">
        <section class="page-hero">
          <div class="page-hero-content">
            <h1 class="page-title">
              <span class="title-line">Our Creative</span>
              <span class="title-line">Portfolio</span>
            </h1>
            <p class="page-description">
              Discover our latest 3D web projects that showcase innovation, 
              creativity, and cutting-edge technology in action.
            </p>
          </div>
        </section>

        <!-- Portfolio Filter -->
        <section class="portfolio-filter">
          <button class="filter-btn active" data-filter="all">All Projects</button>
          <button class="filter-btn" data-filter="web">Web Design</button>
          <button class="filter-btn" data-filter="3d">3D Models</button>
          <button class="filter-btn" data-filter="interactive">Interactive</button>
        </section>

        <!-- Portfolio Grid -->
        <section class="portfolio-grid">
          <div class="portfolio-item" data-category="web interactive">
            <div class="portfolio-image">
              <div class="project-placeholder">🌟</div>
            </div>
            <div class="portfolio-content">
              <h3>Immersive E-commerce</h3>
              <p>3D product visualization platform with AR integration</p>
              <div class="project-tags">
                <span class="tag">Three.js</span>
                <span class="tag">WebXR</span>
                <span class="tag">E-commerce</span>
              </div>
            </div>
          </div>

          <div class="portfolio-item" data-category="3d web">
            <div class="portfolio-image">
              <div class="project-placeholder">🎮</div>
            </div>
            <div class="portfolio-content">
              <h3>Interactive Game Hub</h3>
              <p>Browser-based 3D gaming platform with multiplayer support</p>
              <div class="project-tags">
                <span class="tag">WebGL</span>
                <span class="tag">Multiplayer</span>
                <span class="tag">Gaming</span>
              </div>
            </div>
          </div>

          <div class="portfolio-item" data-category="web 3d">
            <div class="portfolio-image">
              <div class="project-placeholder">🏢</div>
            </div>
            <div class="portfolio-content">
              <h3>Virtual Architecture</h3>
              <p>3D building visualization with virtual tours</p>
              <div class="project-tags">
                <span class="tag">Architecture</span>
                <span class="tag">VR Tours</span>
                <span class="tag">Real Estate</span>
              </div>
            </div>
          </div>

          <div class="portfolio-item" data-category="interactive web">
            <div class="portfolio-image">
              <div class="project-placeholder">🎨</div>
            </div>
            <div class="portfolio-content">
              <h3>Digital Art Gallery</h3>
              <p>Immersive online gallery with 3D artwork displays</p>
              <div class="project-tags">
                <span class="tag">Art</span>
                <span class="tag">Gallery</span>
                <span class="tag">Interactive</span>
              </div>
            </div>
          </div>

          <div class="portfolio-item" data-category="3d interactive">
            <div class="portfolio-image">
              <div class="project-placeholder">🚗</div>
            </div>
            <div class="portfolio-content">
              <h3>Car Configurator</h3>
              <p>Real-time 3D car customization with material editor</p>
              <div class="project-tags">
                <span class="tag">Automotive</span>
                <span class="tag">Configurator</span>
                <span class="tag">Real-time</span>
              </div>
            </div>
          </div>

          <div class="portfolio-item" data-category="web interactive">
            <div class="portfolio-image">
              <div class="project-placeholder">🌍</div>
            </div>
            <div class="portfolio-content">
              <h3>Global Data Visualization</h3>
              <p>Interactive 3D globe with real-time data visualization</p>
              <div class="project-tags">
                <span class="tag">Data Viz</span>
                <span class="tag">Globe</span>
                <span class="tag">Real-time</span>
              </div>
            </div>
          </div>
        </section>

        <!-- Call to Action -->
        <section class="portfolio-cta">
          <h2>Ready to Start Your Project?</h2>
          <p>Let's create something amazing together with cutting-edge 3D web technology.</p>
          <a href="contact.html" class="cta-button">Get Started</a>
        </section>
      </main>
    </div>

    <!-- JavaScript -->
    <script src="script.js"></script>
    <script>
      // Portfolio filter functionality
      document.addEventListener('DOMContentLoaded', function() {
        const filterBtns = document.querySelectorAll('.filter-btn');
        const portfolioItems = document.querySelectorAll('.portfolio-item');

        filterBtns.forEach(btn => {
          btn.addEventListener('click', () => {
            // Remove active class from all buttons
            filterBtns.forEach(b => b.classList.remove('active'));
            // Add active class to clicked button
            btn.classList.add('active');

            const filter = btn.getAttribute('data-filter');

            portfolioItems.forEach(item => {
              if (filter === 'all' || item.getAttribute('data-category').includes(filter)) {
                item.style.display = 'block';
                item.style.animation = 'fadeIn 0.5s ease';
              } else {
                item.style.display = 'none';
              }
            });
          });
        });
      });
    </script>
  </body>
</html>
