* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", "Segoe UI", <PERSON><PERSON>, sans-serif;
  background: radial-gradient(
    ellipse at center,
    #1a1a2e 0%,
    #16213e 50%,
    #0f0f23 100%
  );
  color: #ffffff;
  min-height: 100vh;
  line-height: 1.6;
  perspective: 1000px;
  overflow-x: hidden;
  position: relative;
}

body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
      circle at 20% 80%,
      rgba(120, 119, 198, 0.3) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(255, 119, 198, 0.3) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(120, 219, 255, 0.2) 0%,
      transparent 50%
    );
  z-index: -1;
  animation: floatingBg 20s ease-in-out infinite;
}

@keyframes floatingBg {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(1deg);
  }
}
.image-gradient {
  position: absolute;
  top: 0;
  right: 0;
  opacity: 0.3;
  z-index: -1;
  filter: blur(2px);
  transform: translateZ(-50px);
}

.layer-blur {
  height: 0;
  width: 40rem;
  position: absolute;
  top: 15%;
  right: -10%;
  box-shadow: 0 0 800px 20px rgba(120, 119, 198, 0.4),
    0 0 400px 10px rgba(255, 119, 198, 0.3);
  transform: rotate(-25deg) translateZ(-30px);
  z-index: -1;
  animation: pulse3D 8s ease-in-out infinite;
}

@keyframes pulse3D {
  0%,
  100% {
    transform: rotate(-25deg) translateZ(-30px) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: rotate(-25deg) translateZ(-30px) scale(1.1);
    opacity: 0.8;
  }
}

/*Container*/
.container {
  width: 100%;
  position: relative;
  margin: 0 auto;
  padding: 0 2rem;
  overflow: hidden;
  transform-style: preserve-3d;
}

/*Header*/
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 5rem;
  z-index: 100;
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transform: translateZ(50px);
  transition: all 0.3s ease;
}

header:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateZ(60px);
}

header h1 {
  margin: 0;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #ffffff 0%, #7c3aed 50%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(124, 58, 237, 0.5);
  transform: translateZ(10px);
}

nav ul {
  display: flex;
  list-style: none;
  gap: 3rem;
  transform: translateZ(20px);
}

nav li {
  display: inline-block;
  transform-style: preserve-3d;
}

nav a {
  font-size: 1rem;
  font-weight: 500;
  letter-spacing: 0.1rem;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  text-decoration: none;
  color: rgba(255, 255, 255, 0.8);
  padding: 0.5rem 1rem;
  border-radius: 10px;
  position: relative;
  transform: translateZ(0px);
}

nav a::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(124, 58, 237, 0.2),
    rgba(236, 72, 153, 0.2)
  );
  border-radius: 10px;
  opacity: 0;
  transform: translateZ(-5px);
  transition: all 0.3s ease;
}

nav a:hover {
  color: #ffffff;
  text-shadow: 0 0 20px rgba(124, 58, 237, 0.8);
  transform: translateZ(10px) rotateX(5deg);
}

nav a:hover::before {
  opacity: 1;
  transform: translateZ(-5px) scale(1.1);
}

.btn-signing {
  background: linear-gradient(135deg, #7c3aed 0%, #ec4899 50%, #f59e0b 100%);
  color: white;
  border: none;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  cursor: pointer;
  font-weight: 700;
  letter-spacing: 0.1rem;
  text-transform: uppercase;
  box-shadow: 0 10px 30px rgba(124, 58, 237, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  transform: translateZ(30px);
  transform-style: preserve-3d;
}

.btn-signing::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.6s ease;
  transform: translateZ(1px);
}

.btn-signing::after {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #ec4899 0%, #7c3aed 50%, #3b82f6 100%);
  border-radius: 50px;
  opacity: 0;
  transition: opacity 0.3s ease;
  transform: translateZ(-1px);
}

.btn-signing:hover::before {
  left: 100%;
}

.btn-signing:hover::after {
  opacity: 1;
}

.btn-signing:hover {
  transform: translateZ(40px) rotateX(-5deg) rotateY(5deg);
  box-shadow: 0 20px 50px rgba(124, 58, 237, 0.6),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.btn-signing:active {
  transform: translateZ(35px) rotateX(-2deg) rotateY(2deg);
}

/*Main*/
main {
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: calc(90vh - 8rem);
  transform-style: preserve-3d;
  perspective: 1200px;
}

.content {
  max-width: 45rem;
  margin-left: 8%;
  z-index: 50;
  position: relative;
  padding: 3rem 0;
  transform: translateZ(20px);
  transform-style: preserve-3d;
}

.tag-box {
  position: absolute;
  top: -2rem;
  right: -5rem;
  width: 14rem;
  height: 3rem;
  border-radius: 50px;
  background: linear-gradient(
    45deg,
    #7c3aed,
    #ec4899,
    #f59e0b,
    #10b981,
    #3b82f6,
    #8b5cf6,
    #7c3aed
  );
  background-size: 400%;
  animation: float3D 6s ease-in-out infinite,
    animationGradient 8s linear infinite;
  box-shadow: 0 15px 50px rgba(124, 58, 237, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  transform: translateZ(60px) rotateX(10deg) rotateY(-15deg);
  transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: 100;
  transform-style: preserve-3d;
}

@keyframes float3D {
  0%,
  100% {
    transform: translateZ(60px) rotateX(10deg) rotateY(-15deg) translateY(0px);
  }
  50% {
    transform: translateZ(70px) rotateX(15deg) rotateY(-10deg) translateY(-10px);
  }
}

@keyframes animationGradient {
  to {
    background-position: 200%;
  }
}

.tag-box .tag {
  position: absolute;
  inset: 3px;
  background: rgba(15, 15, 35, 0.8);
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  font-weight: 800;
  letter-spacing: 0.1rem;
  font-size: 0.8rem;
  text-transform: uppercase;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transform: translateZ(5px);
}

.tag-box:hover {
  transform: translateZ(80px) rotateX(0deg) rotateY(0deg) scale(1.15);
  box-shadow: 0 25px 80px rgba(124, 58, 237, 0.8),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.tag-box:hover .tag {
  background: rgba(124, 58, 237, 0.9);
  color: white;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
  transform: translateZ(10px);
}

.tag-box:active {
  transform: translateZ(75px) rotateX(5deg) rotateY(-5deg) scale(1.1);
}

.content h1 {
  font-size: 4.5rem;
  font-weight: 900;
  letter-spacing: 0.05rem;
  margin: 2rem 0 2rem 0;
  line-height: 1.1;
  background: linear-gradient(
    135deg,
    #ffffff 0%,
    #7c3aed 30%,
    #ec4899 70%,
    #f59e0b 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: gradientShift 4s ease-in-out infinite;
  max-width: 100%;
  word-wrap: break-word;
  padding-right: 3rem;
  transform: translateZ(40px);
  text-shadow: 0 0 50px rgba(124, 58, 237, 0.5);
  transform-style: preserve-3d;
}

@keyframes gradientShift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.description {
  font-size: 1.3rem;
  letter-spacing: 0.03rem;
  max-width: 38rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 3rem;
  line-height: 1.7;
  transform: translateZ(30px);
  backdrop-filter: blur(10px);
  padding: 1rem 0;
}

.buttons {
  display: flex;
  gap: 2rem;
  margin-top: 2rem;
  flex-wrap: wrap;
  transform: translateZ(25px);
  transform-style: preserve-3d;
}

/* Main action buttons */
.btn-get-started,
.btn-signing-main {
  display: inline-block;
  padding: 1.2rem 3rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 700;
  letter-spacing: 0.1rem;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  border: none;
  transform: translateZ(0px);
  transform-style: preserve-3d;
  text-transform: uppercase;
  font-size: 0.9rem;
}

.btn-get-started {
  background: linear-gradient(135deg, #7c3aed 0%, #ec4899 50%, #f59e0b 100%);
  color: white;
  box-shadow: 0 15px 40px rgba(124, 58, 237, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.btn-get-started::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.6s ease;
  transform: translateZ(1px);
}

.btn-get-started::after {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #ec4899 0%, #7c3aed 50%, #3b82f6 100%);
  border-radius: 50px;
  opacity: 0;
  transition: opacity 0.4s ease;
  transform: translateZ(-1px);
}

.btn-get-started:hover::before {
  left: 100%;
}

.btn-get-started:hover::after {
  opacity: 1;
}

.btn-get-started:hover {
  transform: translateZ(30px) rotateX(-8deg) rotateY(8deg);
  box-shadow: 0 25px 60px rgba(124, 58, 237, 0.6),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.btn-signing-main {
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  border: 2px solid rgba(124, 58, 237, 0.5);
  box-shadow: 0 15px 40px rgba(124, 58, 237, 0.2),
    inset 0 0 0 1px rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
}

.btn-signing-main::before {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #7c3aed 0%, #ec4899 50%, #f59e0b 100%);
  border-radius: 50px;
  opacity: 0;
  transition: opacity 0.4s ease;
  transform: translateZ(-1px);
}

.btn-signing-main:hover::before {
  opacity: 1;
}

.btn-signing-main:hover {
  color: white;
  border-color: transparent;
  transform: translateZ(30px) rotateX(-8deg) rotateY(-8deg);
  box-shadow: 0 25px 60px rgba(124, 58, 237, 0.6),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.btn-get-started:active {
  transform: translateZ(25px) rotateX(-4deg) rotateY(4deg);
}

.btn-signing-main:active {
  transform: translateZ(25px) rotateX(-4deg) rotateY(-4deg);
}

/* New Elements Styles */

/* Background Elements */
.background-elements {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -10;
  pointer-events: none;
}

.floating-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    rgba(124, 58, 237, 0.1),
    rgba(236, 72, 153, 0.1)
  );
  animation: floatShape 15s ease-in-out infinite;
}

.shape-1 {
  width: 300px;
  height: 300px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 200px;
  height: 200px;
  top: 60%;
  right: 15%;
  animation-delay: 5s;
}

.shape-3 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 60%;
  animation-delay: 10s;
}

@keyframes floatShape {
  0%,
  100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
    opacity: 0.3;
  }
  33% {
    transform: translateY(-30px) translateX(20px) rotate(120deg);
    opacity: 0.6;
  }
  66% {
    transform: translateY(20px) translateX(-15px) rotate(240deg);
    opacity: 0.4;
  }
}

/* Header Updates */
.header {
  position: relative;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.logo-section .logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-text {
  font-weight: 900;
}

.logo-accent {
  font-weight: 300;
  opacity: 0.8;
}

.navigation .nav-list {
  display: flex;
  list-style: none;
  gap: 3rem;
}

.header-actions {
  display: flex;
  align-items: center;
}

.btn-glow {
  position: absolute;
  inset: 0;
  border-radius: 50px;
  background: linear-gradient(
    135deg,
    rgba(124, 58, 237, 0.3),
    rgba(236, 72, 153, 0.3)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  transform: translateZ(-2px);
}

.btn-signing:hover .btn-glow {
  opacity: 1;
}

/* Hero Section */
.hero-section {
  position: relative;
  min-height: 80vh;
  display: flex;
  align-items: center;
}

.hero-text {
  margin-bottom: 3rem;
}

.hero-title {
  font-size: 4.5rem;
  font-weight: 900;
  letter-spacing: 0.05rem;
  margin: 2rem 0;
  line-height: 1.1;
  background: linear-gradient(
    135deg,
    #ffffff 0%,
    #7c3aed 30%,
    #ec4899 70%,
    #f59e0b 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: gradientShift 4s ease-in-out infinite;
  transform: translateZ(40px);
  text-shadow: 0 0 50px rgba(124, 58, 237, 0.5);
}

.title-line {
  display: block;
  transform: translateZ(10px);
}

.hero-description {
  font-size: 1.3rem;
  letter-spacing: 0.03rem;
  max-width: 38rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.7;
  transform: translateZ(30px);
  backdrop-filter: blur(10px);
  padding: 1rem 0;
}

.hero-actions {
  display: flex;
  gap: 2rem;
  margin-bottom: 4rem;
  transform: translateZ(25px);
  flex-wrap: wrap;
}

.btn-shine {
  position: absolute;
  inset: 0;
  border-radius: 50px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  transform: translateZ(1px);
}

.btn-get-started:hover .btn-shine {
  opacity: 1;
}

.btn-border {
  position: absolute;
  inset: 0;
  border-radius: 50px;
  border: 2px solid rgba(124, 58, 237, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease;
  transform: translateZ(-1px);
}

.btn-signing-main:hover .btn-border {
  opacity: 1;
}

/* Stats Section */
.stats-section {
  display: flex;
  gap: 3rem;
  margin-top: 2rem;
  transform: translateZ(20px);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 900;
  background: linear-gradient(135deg, #7c3aed, #ec4899);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.1rem;
}

/* Scroll Indicator */
.scroll-indicator {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  z-index: 50;
}

.scroll-line {
  width: 2px;
  height: 60px;
  background: linear-gradient(to bottom, #7c3aed, transparent);
  animation: scrollPulse 2s ease-in-out infinite;
}

.scroll-text {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  writing-mode: vertical-rl;
  text-orientation: mixed;
  letter-spacing: 0.1rem;
}

@keyframes scrollPulse {
  0%,
  100% {
    opacity: 0.3;
    transform: scaleY(1);
  }
  50% {
    opacity: 1;
    transform: scaleY(1.2);
  }
}

/* Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.5s ease;
}

.loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.loader-ring {
  width: 80px;
  height: 80px;
  border: 3px solid rgba(124, 58, 237, 0.3);
  border-top: 3px solid #7c3aed;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loader-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  letter-spacing: 0.1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 3rem;
  }

  .tag-box {
    right: -2rem;
    width: 12rem;
    height: 2.5rem;
  }

  .hero-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .stats-section {
    flex-direction: column;
    gap: 1.5rem;
  }

  .header-content {
    padding: 1rem 2rem;
  }

  .navigation .nav-list {
    gap: 1.5rem;
  }

  .scroll-indicator {
    display: none;
  }
}
