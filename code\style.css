* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: black;
    color: white;
    min-height: 100vh;
    line-height: 1.5;

}
.image-gradient {
    position: absolute;
    top: 0;
    right: 0;
    opacity: 0.5;
    z-index: -1;
}

.layer-blur {
    height: 0;
    width: 30rem;
    position: absolute;
    top: 20%;
    right: 0;
    box-shadow: 0 0 700px 15px white;
    rotate: -30deg;
    z-index: -1;
}

/*Container*/
.container {
    width: 100%;
   position: relative;
    margin: 0 auto;
    padding: 0 2rem;
    overflow: hidden;
}

/*Header*/
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 5rem;
    z-index: 1;
}

header h1 {
   margin: 0;
   font-size: 3rem;
   font-weight: 300;
}

nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

nav li {
    display: inline-block;
}

nav a {
    font-size: 1rem;
    letter-spacing: 0.1rem;
    transition: color 0.3s ease;
    text-decoration: none;
    color: inherit;
}

nav a:hover {
    color:#a7a7a7;
}

.btn-signing {
    background: linear-gradient(90deg, #333333 0%, #666666 100%);
    color: white;
    border: 1px solid #888888;
    padding: 0.7rem 2rem;
    border-radius: 30px;
    cursor: pointer;
    font-weight: 600;
    letter-spacing: 0.05rem;
    text-transform: uppercase;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.5s ease;
}

.btn-signing:hover {
    background: white;
    color: black;
    border-color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 255, 255, 0.3);
}

.btn-signing:active {
    transform: translateY(1px);
    box-shadow: 0 2px 10px rgba(255, 255, 255, 0.2);
}

/*Main*/
main {
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: calc(90vh - 6rem); 
}

.content {
    max-width: 40rem;
    margin-left: 10%;
    z-index: 999;
}

.tag-box {
    position: relative;
    width: 18rem;
    height: 2.5rem;
    border-radius: 50px;
    background: linear-gradient(to right, #656565,#7f42a7,#6600c5,#5300a0,#757575,#656565);
    background-size: 200%;
    animation: animationGradient 2.5s linear infinite;
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

@keyframes animationGradient {
    to {
        background-position: 200%;
    }
}

.tag-box .tag {
    position: absolute;
    inset: 3px;
    background-color: black;
    border-radius: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.5s ease;
    cursor: pointer;
}

.tag:hover {
    background-color: #5300a0;
}

.content h1 {
    font-size: 4rem;
    font-weight: 600;
    letter-spacing: 0.1rem;
    margin: 1.5rem 0;
    line-height: 1.2;
    text-shadow: 0 0 10px rgba(128, 128, 128, 0.418);
}

.description {
    font-size: 1.2rem;
  letter-spacing: 0.05rem;
  max-width: 35rem;
  color: gray;
}

.buttons {
    display: flex;
    gap: 1rem;
   
}

.btn-get-started {
    text-decoration: none;
    border: 1px solid #2a2a2a;
    font-size: 1rem;
    font-weight: 600;
    padding: 0.7rem 1.2rem;
    position: relative;
   letter-spacing: 0.1rem;
    font-weight: 600;
}

.btn-get-started:after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: white;
    transition: width 0.3s ease;
}

.btn-get-started:hover:after {
    width: 100%;
}

.btn-signing-main {
    background: linear-gradient(90deg, #333333 0%, #666666 100%);
    color: white;
    text-decoration: none;
    padding: 0.7rem 2rem;
    border-radius: 30px;
    font-weight: 600;
    letter-spacing: 0.05rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.5s ease;
    display: inline-block;
}

.btn-signing-main:hover {
    background: white;
    color: black;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 255, 255, 0.3);
}
    



