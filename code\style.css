* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  color: #e8e8f0;
  min-height: 100vh;
  line-height: 1.5;
}
.image-gradient {
  position: absolute;
  top: 0;
  right: 0;
  opacity: 0.5;
  z-index: -1;
}

.layer-blur {
  height: 0;
  width: 30rem;
  position: absolute;
  top: 20%;
  right: 0;
  box-shadow: 0 0 700px 15px rgba(99, 102, 241, 0.3);
  rotate: -30deg;
  z-index: -1;
}

/*Container*/
.container {
  width: 100%;
  position: relative;
  margin: 0 auto;
  padding: 0 2rem;
  overflow: hidden;
}

/*Header*/
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 5rem;
  z-index: 1;
}

header h1 {
  margin: 0;
  font-size: 3rem;
  font-weight: 300;
}

nav ul {
  display: flex;
  list-style: none;
  gap: 2rem;
}

nav li {
  display: inline-block;
}

nav a {
  font-size: 1rem;
  letter-spacing: 0.1rem;
  transition: color 0.3s ease;
  text-decoration: none;
  color: inherit;
}

nav a:hover {
  color: #6366f1;
  text-shadow: 0 0 8px rgba(99, 102, 241, 0.5);
}

.btn-signing {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
  color: white;
  border: 1px solid rgba(99, 102, 241, 0.3);
  padding: 0.7rem 2rem;
  border-radius: 30px;
  cursor: pointer;
  font-weight: 600;
  letter-spacing: 0.05rem;
  text-transform: uppercase;
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-signing::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn-signing:hover::before {
  left: 100%;
}

.btn-signing:hover {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #9333ea 100%);
  border-color: #6366f1;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.6);
}

.btn-signing:active {
  transform: translateY(0px);
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4);
}

/*Main*/
main {
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: calc(90vh - 6rem);
}

.content {
  max-width: 40rem;
  margin-left: 10%;
  z-index: 999;
}

.tag-box {
  position: relative;
  width: 18rem;
  height: 2.5rem;
  border-radius: 50px;
  background: linear-gradient(
    to right,
    #6366f1,
    #8b5cf6,
    #a855f7,
    #d946ef,
    #ec4899,
    #f97316,
    #6366f1
  );
  background-size: 200%;
  animation: animationGradient 3s linear infinite;
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.5);
  margin-bottom: 1rem;
}

@keyframes animationGradient {
  to {
    background-position: 200%;
  }
}

.tag-box .tag {
  position: absolute;
  inset: 3px;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.3s ease;
  cursor: pointer;
  font-weight: 600;
  letter-spacing: 0.1rem;
}

.tag:hover {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  transform: scale(1.02);
}

.content h1 {
  font-size: 3.5rem;
  font-weight: 600;
  letter-spacing: 0.1rem;
  margin: 2rem 0 1.5rem 0;
  line-height: 1.1;
  background: linear-gradient(135deg, #ffffff 0%, #6366f1 50%, #a855f7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(99, 102, 241, 0.3);
  max-width: 100%;
  word-wrap: break-word;
}

.description {
  font-size: 1.2rem;
  letter-spacing: 0.05rem;
  max-width: 35rem;
  color: #9ca3af;
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

.buttons {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

/* Main action buttons */
.btn-get-started,
.btn-signing-main {
  display: inline-block;
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  letter-spacing: 0.05rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
}

.btn-get-started {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4);
}

.btn-get-started::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn-get-started:hover::before {
  left: 100%;
}

.btn-get-started:hover {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.6);
}

.btn-signing-main {
  background: transparent;
  color: #6366f1;
  border: 2px solid #6366f1;
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.2);
}

.btn-signing-main:hover {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  border-color: transparent;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.6);
}

.btn-get-started:active,
.btn-signing-main:active {
  transform: translateY(0px);
}
