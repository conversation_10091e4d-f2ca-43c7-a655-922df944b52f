<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Contact Us - 3D Web Experience</title>
    <meta name="description" content="Get in touch with our 3D web design team for your next project" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet" />
    <link rel="stylesheet" href="style.css" />
    <link rel="stylesheet" href="pages.css" />
  </head>
  <body>
    <!-- Background Elements -->
    <div class="background-elements">
      <div class="layer-blur"></div>
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
      </div>
    </div>

    <!-- Main Container -->
    <div class="container">
      <!-- Header Section -->
      <header class="header">
        <div class="header-content">
          <div class="logo-section">
            <h1 class="logo">
              <a href="index.html">
                <span class="logo-text">3D</span>
                <span class="logo-accent">Web</span>
              </a>
            </h1>
          </div>
          
          <nav class="navigation" role="navigation">
            <ul class="nav-list">
              <li class="nav-item"><a href="about.html" class="nav-link">About</a></li>
              <li class="nav-item"><a href="features.html" class="nav-link">Features</a></li>
              <li class="nav-item"><a href="portfolio.html" class="nav-link">Portfolio</a></li>
              <li class="nav-item"><a href="contact.html" class="nav-link active">Contact</a></li>
            </ul>
          </nav>
          
          <div class="header-actions">
            <button class="btn-signing" aria-label="Sign In">
              <span>Sign In</span>
              <div class="btn-glow"></div>
            </button>
          </div>
        </div>
      </header>

      <!-- Main Content -->
      <main class="main-content page-content">
        <section class="page-hero">
          <div class="page-hero-content">
            <h1 class="page-title">
              <span class="title-line">Let's Create</span>
              <span class="title-line">Together</span>
            </h1>
            <p class="page-description">
              Ready to bring your vision to life with stunning 3D web experiences? 
              Get in touch and let's discuss your next project.
            </p>
          </div>
        </section>

        <!-- Contact Section -->
        <section class="contact-section">
          <div class="contact-container">
            <!-- Contact Form -->
            <div class="contact-form-wrapper">
              <h2>Send us a Message</h2>
              <form class="contact-form" id="contactForm">
                <div class="form-group">
                  <label for="name">Full Name</label>
                  <input type="text" id="name" name="name" required>
                </div>
                
                <div class="form-group">
                  <label for="email">Email Address</label>
                  <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-group">
                  <label for="company">Company (Optional)</label>
                  <input type="text" id="company" name="company">
                </div>
                
                <div class="form-group">
                  <label for="project">Project Type</label>
                  <select id="project" name="project" required>
                    <option value="">Select Project Type</option>
                    <option value="web-design">3D Web Design</option>
                    <option value="interactive">Interactive Experience</option>
                    <option value="ecommerce">E-commerce Solution</option>
                    <option value="game">Web Game Development</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                
                <div class="form-group">
                  <label for="budget">Budget Range</label>
                  <select id="budget" name="budget">
                    <option value="">Select Budget Range</option>
                    <option value="5k-10k">$5,000 - $10,000</option>
                    <option value="10k-25k">$10,000 - $25,000</option>
                    <option value="25k-50k">$25,000 - $50,000</option>
                    <option value="50k+">$50,000+</option>
                  </select>
                </div>
                
                <div class="form-group">
                  <label for="message">Project Details</label>
                  <textarea id="message" name="message" rows="5" required placeholder="Tell us about your project vision, goals, and requirements..."></textarea>
                </div>
                
                <button type="submit" class="submit-btn">
                  <span>Send Message</span>
                  <div class="btn-shine"></div>
                </button>
              </form>
            </div>

            <!-- Contact Info -->
            <div class="contact-info">
              <h2>Get in Touch</h2>
              
              <div class="contact-item">
                <div class="contact-icon">📧</div>
                <div class="contact-details">
                  <h3>Email</h3>
                  <p><EMAIL></p>
                </div>
              </div>
              
              <div class="contact-item">
                <div class="contact-icon">📱</div>
                <div class="contact-details">
                  <h3>Phone</h3>
                  <p>+****************</p>
                </div>
              </div>
              
              <div class="contact-item">
                <div class="contact-icon">📍</div>
                <div class="contact-details">
                  <h3>Location</h3>
                  <p>San Francisco, CA<br>New York, NY</p>
                </div>
              </div>
              
              <div class="contact-item">
                <div class="contact-icon">⏰</div>
                <div class="contact-details">
                  <h3>Response Time</h3>
                  <p>Within 24 hours</p>
                </div>
              </div>

              <!-- Social Links -->
              <div class="social-links">
                <h3>Follow Us</h3>
                <div class="social-grid">
                  <a href="#" class="social-link">🐦 Twitter</a>
                  <a href="#" class="social-link">💼 LinkedIn</a>
                  <a href="#" class="social-link">📷 Instagram</a>
                  <a href="#" class="social-link">🎮 GitHub</a>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- FAQ Section -->
        <section class="faq-section">
          <h2 class="section-title">Frequently Asked Questions</h2>
          <div class="faq-grid">
            <div class="faq-item">
              <h3>How long does a typical 3D web project take?</h3>
              <p>Project timelines vary based on complexity, but most projects take 4-12 weeks from concept to launch.</p>
            </div>
            
            <div class="faq-item">
              <h3>Do you provide ongoing support?</h3>
              <p>Yes! We offer comprehensive support packages including updates, optimization, and technical assistance.</p>
            </div>
            
            <div class="faq-item">
              <h3>Can you work with existing websites?</h3>
              <p>Absolutely! We can integrate 3D elements into existing websites or completely redesign them.</p>
            </div>
            
            <div class="faq-item">
              <h3>What technologies do you use?</h3>
              <p>We use cutting-edge technologies like Three.js, WebGL, GSAP, and modern web frameworks.</p>
            </div>
          </div>
        </section>
      </main>
    </div>

    <!-- JavaScript -->
    <script src="script.js"></script>
    <script>
      // Contact form handling
      document.getElementById('contactForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Simple form validation and submission simulation
        const formData = new FormData(this);
        const submitBtn = this.querySelector('.submit-btn');
        
        submitBtn.innerHTML = '<span>Sending...</span>';
        submitBtn.disabled = true;
        
        // Simulate form submission
        setTimeout(() => {
          alert('Thank you for your message! We\'ll get back to you within 24 hours.');
          this.reset();
          submitBtn.innerHTML = '<span>Send Message</span><div class="btn-shine"></div>';
          submitBtn.disabled = false;
        }, 2000);
      });
    </script>
  </body>
</html>
