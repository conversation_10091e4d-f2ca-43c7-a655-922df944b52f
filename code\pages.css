/* Pages Specific Styles */

/* Active Navigation Link */
.nav-link.active {
  color: #ffffff !important;
  background: linear-gradient(135deg, #7c3aed 0%, #ec4899 100%) !important;
  transform: translateY(-2px);
}

.nav-link.active::before {
  opacity: 1 !important;
  transform: scale(1) !important;
}

/* Logo Link Styles */
.logo a {
  text-decoration: none;
  color: inherit;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Page Content Styles */
.page-content {
  padding-top: 8rem;
  min-height: 100vh;
}

.page-hero {
  text-align: center;
  padding: 4rem 0 6rem 0;
  max-width: 800px;
  margin: 0 auto;
}

.page-hero-content {
  transform: translateZ(30px);
}

.page-title {
  font-size: 4rem;
  font-weight: 900;
  letter-spacing: 0.05rem;
  margin-bottom: 2rem;
  line-height: 1.1;
  background: linear-gradient(
    135deg,
    #ffffff 0%,
    #7c3aed 30%,
    #ec4899 70%,
    #f59e0b 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: gradientShift 4s ease-in-out infinite;
}

.title-line {
  display: block;
}

.page-description {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.7;
  max-width: 600px;
  margin: 0 auto;
}

/* Section Titles */
.section-title {
  font-size: 2.5rem;
  font-weight: 800;
  text-align: center;
  margin-bottom: 3rem;
  background: linear-gradient(135deg, #ffffff 0%, #7c3aed 50%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* About Page Styles */
.about-sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin: 4rem 0;
  padding: 0 2rem;
}

.about-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2.5rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  transform: translateZ(20px);
}

.about-card:hover {
  transform: translateZ(30px) translateY(-10px);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 20px 40px rgba(124, 58, 237, 0.3);
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  display: block;
}

.about-card h2 {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #ffffff;
}

.about-card p {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.about-card ul {
  list-style: none;
  padding: 0;
}

.about-card li {
  color: rgba(255, 255, 255, 0.7);
  padding: 0.5rem 0;
  position: relative;
  padding-left: 1.5rem;
}

.about-card li::before {
  content: "✨";
  position: absolute;
  left: 0;
  top: 0.5rem;
}

/* Team Section */
.team-section {
  padding: 4rem 2rem;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.team-member {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  transform: translateZ(20px);
}

.team-member:hover {
  transform: translateZ(30px) translateY(-10px);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 20px 40px rgba(124, 58, 237, 0.3);
}

.member-avatar {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.team-member h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #ffffff;
}

.member-role {
  color: #7c3aed;
  font-weight: 600;
  margin-bottom: 1rem;
}

.member-bio {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
}

/* Stats Showcase */
.stats-showcase {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin: 4rem 2rem;
  padding: 3rem 0;
}

/* Features Page Styles */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin: 4rem 2rem;
}

.feature-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2.5rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  transform: translateZ(20px);
}

.feature-card:hover {
  transform: translateZ(30px) translateY(-10px);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 20px 40px rgba(124, 58, 237, 0.3);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  display: block;
}

.feature-card h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #ffffff;
}

.feature-card p {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.feature-list {
  list-style: none;
  padding: 0;
}

.feature-list li {
  color: rgba(255, 255, 255, 0.6);
  padding: 0.3rem 0;
  position: relative;
  padding-left: 1.5rem;
  font-size: 0.9rem;
}

.feature-list li::before {
  content: "→";
  position: absolute;
  left: 0;
  color: #7c3aed;
  font-weight: bold;
}

/* Technology Stack */
.tech-stack {
  padding: 4rem 2rem;
  text-align: center;
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.tech-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 2rem 1rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  transform: translateZ(20px);
}

.tech-item:hover {
  transform: translateZ(30px) translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 15px 30px rgba(124, 58, 237, 0.3);
}

.tech-logo {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.tech-item span {
  font-weight: 600;
  color: #ffffff;
}

/* Portfolio Page Styles */
.portfolio-filter {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0 4rem 0;
  flex-wrap: wrap;
}

.filter-btn {
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.filter-btn:hover,
.filter-btn.active {
  background: linear-gradient(135deg, #7c3aed 0%, #ec4899 100%);
  color: #ffffff;
  border-color: transparent;
  transform: translateY(-2px);
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin: 0 2rem 4rem 2rem;
}

.portfolio-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  overflow: hidden;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  transform: translateZ(20px);
}

.portfolio-item:hover {
  transform: translateZ(30px) translateY(-10px);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 20px 40px rgba(124, 58, 237, 0.3);
}

.portfolio-image {
  height: 200px;
  background: linear-gradient(
    135deg,
    rgba(124, 58, 237, 0.3),
    rgba(236, 72, 153, 0.3)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.project-placeholder {
  font-size: 4rem;
  opacity: 0.8;
}

.portfolio-content {
  padding: 2rem;
}

.portfolio-content h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #ffffff;
}

.portfolio-content p {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.project-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.tag {
  background: rgba(124, 58, 237, 0.2);
  color: #ffffff;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid rgba(124, 58, 237, 0.3);
}

/* Portfolio CTA */
.portfolio-cta {
  text-align: center;
  padding: 4rem 2rem;
  background: rgba(255, 255, 255, 0.03);
  margin: 4rem 2rem;
  border-radius: 20px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.portfolio-cta h2 {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #ffffff 0%, #7c3aed 50%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.portfolio-cta p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.2rem;
  margin-bottom: 2rem;
}

.cta-button {
  display: inline-block;
  background: linear-gradient(135deg, #7c3aed 0%, #ec4899 100%);
  color: white;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  transform: translateZ(20px);
}

.cta-button:hover {
  transform: translateZ(30px) translateY(-5px);
  box-shadow: 0 15px 30px rgba(124, 58, 237, 0.5);
  background: linear-gradient(135deg, #8b5cf6 0%, #f472b6 100%);
}

/* Fade in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Contact Page Styles */
.contact-section {
  padding: 4rem 2rem;
}

.contact-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  max-width: 1200px;
  margin: 0 auto;
}

.contact-form-wrapper {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 3rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transform: translateZ(20px);
}

.contact-form-wrapper h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2rem;
  color: #ffffff;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #7c3aed;
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.2);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.submit-btn {
  width: 100%;
  background: linear-gradient(135deg, #7c3aed 0%, #ec4899 100%);
  color: white;
  border: none;
  padding: 1.2rem 2rem;
  border-radius: 50px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(124, 58, 237, 0.5);
  background: linear-gradient(135deg, #8b5cf6 0%, #f472b6 100%);
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Contact Info */
.contact-info {
  padding: 2rem 0;
}

.contact-info h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2rem;
  color: #ffffff;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.contact-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
}

.contact-icon {
  font-size: 1.5rem;
  margin-top: 0.2rem;
}

.contact-details h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #ffffff;
}

.contact-details p {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
}

/* Social Links */
.social-links {
  margin-top: 3rem;
}

.social-links h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #ffffff;
}

.social-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.social-link {
  display: block;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  text-decoration: none;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  text-align: center;
  font-weight: 500;
}

.social-link:hover {
  background: linear-gradient(135deg, #7c3aed 0%, #ec4899 100%);
  color: #ffffff;
  transform: translateY(-2px);
}

/* FAQ Section */
.faq-section {
  padding: 4rem 2rem;
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.faq-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 2rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.faq-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(124, 58, 237, 0.2);
}

.faq-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #ffffff;
}

.faq-item p {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
}

/* Responsive Design for Pages */
@media (max-width: 768px) {
  .page-title {
    font-size: 3rem;
  }

  .about-sections,
  .features-grid,
  .portfolio-grid {
    grid-template-columns: 1fr;
    margin: 2rem 1rem;
  }

  .about-card,
  .feature-card,
  .team-member {
    padding: 2rem;
  }

  .portfolio-filter {
    margin: 2rem 1rem;
  }

  .portfolio-cta {
    margin: 2rem 1rem;
    padding: 3rem 1.5rem;
  }

  .tech-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }

  .contact-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    margin: 0 1rem;
  }

  .contact-form-wrapper {
    padding: 2rem;
  }

  .social-grid {
    grid-template-columns: 1fr;
  }

  .faq-grid {
    grid-template-columns: 1fr;
    margin: 2rem 1rem 0 1rem;
  }
}
