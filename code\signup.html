<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Sign Up - 3D Web Experience</title>
    <meta name="description" content="Create your account to access amazing 3D web experiences" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet" />
    <link rel="stylesheet" href="style.css" />
    <link rel="stylesheet" href="pages.css" />
    <link rel="stylesheet" href="signin.css" />
  </head>
  <body class="signin-body">
    <!-- Background Elements -->
    <div class="background-elements">
      <div class="layer-blur signin-blur"></div>
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
      </div>
      <div class="signin-particles">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
      </div>
    </div>

    <!-- Main Container -->
    <div class="signin-container">
      <!-- Header Section -->
      <header class="header signin-header">
        <div class="header-content">
          <div class="logo-section">
            <h1 class="logo">
              <a href="index.html">
                <span class="logo-text">3D</span>
                <span class="logo-accent">Web</span>
              </a>
            </h1>
          </div>
          
          <nav class="navigation" role="navigation">
            <ul class="nav-list">
              <li class="nav-item"><a href="about.html" class="nav-link">About</a></li>
              <li class="nav-item"><a href="features.html" class="nav-link">Features</a></li>
              <li class="nav-item"><a href="portfolio.html" class="nav-link">Portfolio</a></li>
              <li class="nav-item"><a href="contact.html" class="nav-link">Contact</a></li>
            </ul>
          </nav>
          
          <div class="header-actions">
            <a href="signin.html" class="btn-signup">Sign In</a>
          </div>
        </div>
      </header>

      <!-- Main Content -->
      <main class="signin-main">
        <div class="signin-content">
          <!-- Left Side - Welcome -->
          <div class="signin-welcome">
            <div class="welcome-content">
              <h1 class="welcome-title">
                <span class="title-line">Join the</span>
                <span class="title-line">Future</span>
              </h1>
              <p class="welcome-description">
                Create your account and unlock the power of 3D web experiences. 
                Start building amazing interactive projects today.
              </p>
              
              <div class="welcome-features">
                <div class="feature-item">
                  <div class="feature-icon">🎯</div>
                  <span>Free Account Setup</span>
                </div>
                <div class="feature-item">
                  <div class="feature-icon">🚀</div>
                  <span>Instant Access</span>
                </div>
                <div class="feature-item">
                  <div class="feature-icon">💎</div>
                  <span>Premium Features</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Right Side - Sign Up Form -->
          <div class="signin-form-container">
            <div class="signin-form-wrapper">
              <div class="form-header">
                <h2>Create Account</h2>
                <p>Fill in your details to get started</p>
              </div>

              <form class="signin-form" id="signupForm">
                <div class="form-group">
                  <label for="fullName">Full Name</label>
                  <div class="input-wrapper">
                    <input type="text" id="fullName" name="fullName" required placeholder="Enter your full name">
                    <div class="input-icon">👤</div>
                  </div>
                </div>

                <div class="form-group">
                  <label for="email">Email Address</label>
                  <div class="input-wrapper">
                    <input type="email" id="email" name="email" required placeholder="Enter your email">
                    <div class="input-icon">📧</div>
                  </div>
                </div>
                
                <div class="form-group">
                  <label for="password">Password</label>
                  <div class="input-wrapper">
                    <input type="password" id="password" name="password" required placeholder="Create a password">
                    <div class="input-icon">🔒</div>
                    <button type="button" class="toggle-password" id="togglePassword">👁️</button>
                  </div>
                </div>

                <div class="form-group">
                  <label for="confirmPassword">Confirm Password</label>
                  <div class="input-wrapper">
                    <input type="password" id="confirmPassword" name="confirmPassword" required placeholder="Confirm your password">
                    <div class="input-icon">🔐</div>
                    <button type="button" class="toggle-password" id="toggleConfirmPassword">👁️</button>
                  </div>
                </div>

                <div class="form-options">
                  <label class="checkbox-wrapper">
                    <input type="checkbox" id="terms" name="terms" required>
                    <span class="checkmark"></span>
                    I agree to the <a href="#" class="forgot-password">Terms & Conditions</a>
                  </label>
                </div>
                
                <button type="submit" class="signin-btn">
                  <span>Create Account</span>
                  <div class="btn-glow"></div>
                </button>

                <div class="divider">
                  <span>or sign up with</span>
                </div>

                <div class="social-signin">
                  <button type="button" class="social-btn google-btn">
                    <div class="social-icon">🌐</div>
                    <span>Google</span>
                  </button>
                  <button type="button" class="social-btn github-btn">
                    <div class="social-icon">⚡</div>
                    <span>GitHub</span>
                  </button>
                  <button type="button" class="social-btn apple-btn">
                    <div class="social-icon">🍎</div>
                    <span>Apple</span>
                  </button>
                </div>

                <div class="signup-link">
                  <p>Already have an account? <a href="signin.html">Sign in here</a></p>
                </div>
              </form>
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- Success Modal -->
    <div class="modal" id="successModal">
      <div class="modal-content">
        <div class="modal-icon">🎉</div>
        <h3>Account Created!</h3>
        <p>Welcome to the 3D Web Experience! Your account has been successfully created.</p>
        <button class="modal-btn" id="continueBtn">Continue to Dashboard</button>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
      <div class="loading-spinner">
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
      </div>
      <p>Creating your account...</p>
    </div>

    <!-- JavaScript -->
    <script src="script.js"></script>
    <script>
      // Sign up form handling
      document.getElementById('signupForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const loadingOverlay = document.getElementById('loadingOverlay');
        const successModal = document.getElementById('successModal');
        
        // Password validation
        if (password !== confirmPassword) {
          alert('Passwords do not match!');
          return;
        }
        
        if (password.length < 6) {
          alert('Password must be at least 6 characters long!');
          return;
        }
        
        // Show loading
        loadingOverlay.style.display = 'flex';
        
        // Simulate account creation
        setTimeout(() => {
          loadingOverlay.style.display = 'none';
          successModal.style.display = 'flex';
          
          // Add success animation
          successModal.querySelector('.modal-content').style.animation = 'modalSlideIn 0.5s ease';
        }, 2500);
      });

      // Continue button
      document.getElementById('continueBtn').addEventListener('click', function() {
        window.location.href = 'signin.html'; // Redirect to sign in
      });

      // Password toggle functionality
      function setupPasswordToggle(inputId, toggleId) {
        document.getElementById(toggleId).addEventListener('click', function() {
          const passwordInput = document.getElementById(inputId);
          const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
          passwordInput.setAttribute('type', type);
          this.textContent = type === 'password' ? '👁️' : '🙈';
        });
      }

      setupPasswordToggle('password', 'togglePassword');
      setupPasswordToggle('confirmPassword', 'toggleConfirmPassword');

      // Social sign up buttons
      document.querySelectorAll('.social-btn').forEach(btn => {
        btn.addEventListener('click', function() {
          const provider = this.querySelector('span').textContent;
          alert(`Sign up with ${provider} - Feature coming soon!`);
        });
      });

      // Add floating animation to particles
      document.addEventListener('DOMContentLoaded', function() {
        const particles = document.querySelectorAll('.particle');
        particles.forEach((particle, index) => {
          particle.style.animationDelay = `${index * 0.5}s`;
        });
      });
    </script>
  </body>
</html>
