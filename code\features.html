<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Features - 3D Web Experience</title>
    <meta
      name="description"
      content="Discover our cutting-edge 3D web features and capabilities"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="style.css" />
    <link rel="stylesheet" href="pages.css" />
  </head>
  <body>
    <!-- Background Elements -->
    <div class="background-elements">
      <div class="layer-blur"></div>
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
      </div>
    </div>

    <!-- Main Container -->
    <div class="container">
      <!-- Header Section -->
      <header class="header">
        <div class="header-content">
          <div class="logo-section">
            <h1 class="logo">
              <a href="index.html">
                <span class="logo-text">3D</span>
                <span class="logo-accent">Web</span>
              </a>
            </h1>
          </div>

          <nav class="navigation" role="navigation">
            <ul class="nav-list">
              <li class="nav-item">
                <a href="about.html" class="nav-link">About</a>
              </li>
              <li class="nav-item">
                <a href="features.html" class="nav-link active">Features</a>
              </li>
              <li class="nav-item">
                <a href="portfolio.html" class="nav-link">Portfolio</a>
              </li>
              <li class="nav-item">
                <a href="contact.html" class="nav-link">Contact</a>
              </li>
            </ul>
          </nav>

          <div class="header-actions">
            <a href="signin.html" class="btn-signing" aria-label="Sign In">
              <span>Sign In</span>
              <div class="btn-glow"></div>
            </a>
          </div>
        </div>
      </header>

      <!-- Main Content -->
      <main class="main-content page-content">
        <section class="page-hero">
          <div class="page-hero-content">
            <h1 class="page-title">
              <span class="title-line">Powerful</span>
              <span class="title-line">3D Features</span>
            </h1>
            <p class="page-description">
              Explore our comprehensive suite of 3D web technologies and
              interactive features that bring your digital vision to life.
            </p>
          </div>
        </section>

        <!-- Features Grid -->
        <section class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">🎮</div>
            <h3>Interactive 3D Models</h3>
            <p>
              High-performance 3D models with real-time interaction, rotation,
              and animation capabilities.
            </p>
            <ul class="feature-list">
              <li>WebGL optimization</li>
              <li>Real-time rendering</li>
              <li>Touch & mouse controls</li>
            </ul>
          </div>

          <div class="feature-card">
            <div class="feature-icon">🌈</div>
            <h3>Advanced Lighting</h3>
            <p>
              Dynamic lighting systems with realistic shadows, reflections, and
              environmental effects.
            </p>
            <ul class="feature-list">
              <li>PBR materials</li>
              <li>Dynamic shadows</li>
              <li>HDR environments</li>
            </ul>
          </div>

          <div class="feature-card">
            <div class="feature-icon">⚡</div>
            <h3>Performance Optimized</h3>
            <p>
              Optimized for all devices with intelligent LOD systems and
              efficient resource management.
            </p>
            <ul class="feature-list">
              <li>60fps performance</li>
              <li>Mobile optimization</li>
              <li>Progressive loading</li>
            </ul>
          </div>

          <div class="feature-card">
            <div class="feature-icon">🎨</div>
            <h3>Custom Animations</h3>
            <p>
              Smooth, physics-based animations with timeline control and
              interactive triggers.
            </p>
            <ul class="feature-list">
              <li>Keyframe animation</li>
              <li>Physics simulation</li>
              <li>Event-driven triggers</li>
            </ul>
          </div>

          <div class="feature-card">
            <div class="feature-icon">🔧</div>
            <h3>Developer Tools</h3>
            <p>
              Comprehensive development toolkit with debugging, profiling, and
              optimization tools.
            </p>
            <ul class="feature-list">
              <li>Performance profiler</li>
              <li>Debug visualizations</li>
              <li>Asset optimization</li>
            </ul>
          </div>

          <div class="feature-card">
            <div class="feature-icon">🌐</div>
            <h3>Cross-Platform</h3>
            <p>
              Universal compatibility across all modern browsers and devices
              with responsive design.
            </p>
            <ul class="feature-list">
              <li>Browser compatibility</li>
              <li>Responsive design</li>
              <li>Progressive enhancement</li>
            </ul>
          </div>
        </section>

        <!-- Technology Stack -->
        <section class="tech-stack">
          <h2 class="section-title">Technology Stack</h2>
          <div class="tech-grid">
            <div class="tech-item">
              <div class="tech-logo">⚛️</div>
              <span>Three.js</span>
            </div>
            <div class="tech-item">
              <div class="tech-logo">🎯</div>
              <span>WebGL</span>
            </div>
            <div class="tech-item">
              <div class="tech-logo">🚀</div>
              <span>GSAP</span>
            </div>
            <div class="tech-item">
              <div class="tech-logo">💎</div>
              <span>Blender</span>
            </div>
            <div class="tech-item">
              <div class="tech-logo">⚡</div>
              <span>WebAssembly</span>
            </div>
            <div class="tech-item">
              <div class="tech-logo">🎨</div>
              <span>Babylon.js</span>
            </div>
          </div>
        </section>
      </main>
    </div>

    <!-- JavaScript -->
    <script src="script.js"></script>
  </body>
</html>
