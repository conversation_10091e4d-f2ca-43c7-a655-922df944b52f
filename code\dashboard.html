<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dashboard - 3D Web Experience</title>
    <meta name="description" content="Your 3D web experience dashboard" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet" />
    <link rel="stylesheet" href="style.css" />
    <link rel="stylesheet" href="pages.css" />
  </head>
  <body>
    <!-- Background Elements -->
    <div class="background-elements">
      <div class="layer-blur"></div>
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
      </div>
    </div>

    <!-- Main Container -->
    <div class="container">
      <!-- Header Section -->
      <header class="header">
        <div class="header-content">
          <div class="logo-section">
            <h1 class="logo">
              <a href="index.html">
                <span class="logo-text">3D</span>
                <span class="logo-accent">Web</span>
              </a>
            </h1>
          </div>
          
          <nav class="navigation" role="navigation">
            <ul class="nav-list">
              <li class="nav-item"><a href="about.html" class="nav-link">About</a></li>
              <li class="nav-item"><a href="features.html" class="nav-link">Features</a></li>
              <li class="nav-item"><a href="portfolio.html" class="nav-link">Portfolio</a></li>
              <li class="nav-item"><a href="contact.html" class="nav-link">Contact</a></li>
            </ul>
          </nav>
          
          <div class="header-actions">
            <button class="btn-signing" onclick="signOut()">
              <span>Sign Out</span>
            </button>
          </div>
        </div>
      </header>

      <!-- Main Content -->
      <main class="main-content page-content">
        <section class="page-hero">
          <div class="page-hero-content">
            <h1 class="page-title">
              <span class="title-line">Welcome to</span>
              <span class="title-line">Your Dashboard</span>
            </h1>
            <p class="page-description">
              Manage your 3D projects, explore new features, and create amazing experiences.
            </p>
          </div>
        </section>

        <!-- Dashboard Content -->
        <section class="dashboard-content">
          <div class="dashboard-grid">
            <!-- Quick Stats -->
            <div class="dashboard-card stats-card">
              <h3>📊 Quick Stats</h3>
              <div class="stats-grid">
                <div class="stat-item">
                  <span class="stat-number">12</span>
                  <span class="stat-label">Projects</span>
                </div>
                <div class="stat-item">
                  <span class="stat-number">45</span>
                  <span class="stat-label">Assets</span>
                </div>
                <div class="stat-item">
                  <span class="stat-number">8.5k</span>
                  <span class="stat-label">Views</span>
                </div>
              </div>
            </div>

            <!-- Recent Projects -->
            <div class="dashboard-card projects-card">
              <h3>🎨 Recent Projects</h3>
              <div class="project-list">
                <div class="project-item">
                  <div class="project-icon">🌟</div>
                  <div class="project-info">
                    <h4>3D Product Showcase</h4>
                    <p>Interactive product visualization</p>
                  </div>
                  <div class="project-status">Active</div>
                </div>
                <div class="project-item">
                  <div class="project-icon">🎮</div>
                  <div class="project-info">
                    <h4>Web Game Demo</h4>
                    <p>Browser-based 3D game</p>
                  </div>
                  <div class="project-status">Draft</div>
                </div>
                <div class="project-item">
                  <div class="project-icon">🏢</div>
                  <div class="project-info">
                    <h4>Virtual Tour</h4>
                    <p>360° building walkthrough</p>
                  </div>
                  <div class="project-status">Published</div>
                </div>
              </div>
            </div>

            <!-- Quick Actions -->
            <div class="dashboard-card actions-card">
              <h3>⚡ Quick Actions</h3>
              <div class="action-buttons">
                <button class="action-btn">
                  <div class="action-icon">➕</div>
                  <span>New Project</span>
                </button>
                <button class="action-btn">
                  <div class="action-icon">📁</div>
                  <span>Browse Assets</span>
                </button>
                <button class="action-btn">
                  <div class="action-icon">🎓</div>
                  <span>Tutorials</span>
                </button>
                <button class="action-btn">
                  <div class="action-icon">⚙️</div>
                  <span>Settings</span>
                </button>
              </div>
            </div>

            <!-- Activity Feed -->
            <div class="dashboard-card activity-card">
              <h3>📈 Recent Activity</h3>
              <div class="activity-list">
                <div class="activity-item">
                  <div class="activity-time">2 hours ago</div>
                  <div class="activity-text">Published "3D Product Showcase"</div>
                </div>
                <div class="activity-item">
                  <div class="activity-time">1 day ago</div>
                  <div class="activity-text">Updated project settings</div>
                </div>
                <div class="activity-item">
                  <div class="activity-time">3 days ago</div>
                  <div class="activity-text">Created new 3D model</div>
                </div>
                <div class="activity-item">
                  <div class="activity-time">1 week ago</div>
                  <div class="activity-text">Joined 3D Web Experience</div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>

    <!-- JavaScript -->
    <script src="script.js"></script>
    <script>
      function signOut() {
        if (confirm('Are you sure you want to sign out?')) {
          alert('You have been signed out successfully!');
          window.location.href = 'index.html';
        }
      }

      // Add click handlers for action buttons
      document.querySelectorAll('.action-btn').forEach(btn => {
        btn.addEventListener('click', function() {
          const action = this.querySelector('span').textContent;
          alert(`${action} - Feature coming soon!`);
        });
      });

      // Add click handlers for project items
      document.querySelectorAll('.project-item').forEach(item => {
        item.addEventListener('click', function() {
          const projectName = this.querySelector('h4').textContent;
          alert(`Opening ${projectName} - Feature coming soon!`);
        });
      });
    </script>

    <style>
      /* Dashboard Specific Styles */
      .dashboard-content {
        padding: 2rem;
        max-width: 1400px;
        margin: 0 auto;
      }

      .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
      }

      .dashboard-card {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 20px;
        padding: 2rem;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        transform: translateZ(20px);
      }

      .dashboard-card:hover {
        background: rgba(255, 255, 255, 0.08);
        transform: translateZ(30px) translateY(-5px);
        box-shadow: 0 15px 40px rgba(124, 58, 237, 0.3);
      }

      .dashboard-card h3 {
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        color: #ffffff;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
      }

      .project-list,
      .activity-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }

      .project-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .project-item:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateX(5px);
      }

      .project-icon {
        font-size: 1.5rem;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #7c3aed, #ec4899);
        border-radius: 8px;
      }

      .project-info {
        flex: 1;
      }

      .project-info h4 {
        margin: 0 0 0.2rem 0;
        color: #ffffff;
        font-size: 1rem;
      }

      .project-info p {
        margin: 0;
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.9rem;
      }

      .project-status {
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
      }

      .action-buttons {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
      }

      .action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        padding: 1.5rem 1rem;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        color: rgba(255, 255, 255, 0.8);
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
      }

      .action-btn:hover {
        background: linear-gradient(135deg, #7c3aed, #ec4899);
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(124, 58, 237, 0.4);
      }

      .action-icon {
        font-size: 1.5rem;
      }

      .activity-item {
        padding: 1rem 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .activity-item:last-child {
        border-bottom: none;
      }

      .activity-time {
        font-size: 0.8rem;
        color: rgba(255, 255, 255, 0.5);
        margin-bottom: 0.3rem;
      }

      .activity-text {
        color: rgba(255, 255, 255, 0.8);
      }

      @media (max-width: 768px) {
        .dashboard-grid {
          grid-template-columns: 1fr;
        }
        
        .stats-grid {
          grid-template-columns: repeat(2, 1fr);
        }
        
        .action-buttons {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </body>
</html>
